'''
    自稳步进云台控制系统
    1号电机控制水平方向旋转（Yaw轴）
    2号电机控制竖直方向旋转（Pitch轴）
    通过IMU数据实现云台自稳定
'''

from maix import ahrs, app, time
from maix.ext_dev import imu
from motor_driver import MotorDriver
import math

class StabilizedGimbal:
    def __init__(self):
        # IMU AHRS滤波器参数
        self.kp = 2.0      # 比例增益
        self.ki = 0.01     # 积分增益

        # PID控制器参数
        self.pid_pitch = {
            'kp': 1.5,     # 比例系数
            'ki': 0.1,     # 积分系数
            'kd': 0.05,    # 微分系数
            'integral': 0.0,
            'last_error': 0.0,
            'max_integral': 100.0
        }

        self.pid_roll = {
            'kp': 1.5,
            'ki': 0.1,
            'kd': 0.05,
            'integral': 0.0,
            'last_error': 0.0,
            'max_integral': 100.0
        }

        # 电机参数
        self.motor_speed = 200      # 电机转速 (RPM)
        self.motor_acceleration = 50 # 电机加速度
        self.angle_to_pulse = 65536.0 / 360.0  # 角度到脉冲转换比例

        # 目标角度和当前角度
        self.target_pitch = 0.0
        self.target_roll = 0.0
        self.current_pitch = 0.0
        self.current_roll = 0.0

        # 初始化标志
        self.is_initialized = False
        self.stabilization_enabled = False

        print("初始化IMU传感器...")
        # 初始化IMU传感器
        self.sensor = imu.IMU("qmi8658", mode=imu.Mode.DUAL,
                             acc_scale=imu.AccScale.ACC_SCALE_2G,
                             acc_odr=imu.AccOdr.ACC_ODR_1000,
                             gyro_scale=imu.GyroScale.GYRO_SCALE_256DPS,
                             gyro_odr=imu.GyroOdr.GYRO_ODR_8000)

        # 初始化AHRS滤波器
        self.ahrs_filter = ahrs.MahonyAHRS(self.kp, self.ki)

        print("初始化电机驱动...")
        # 初始化电机驱动
        self.motor = MotorDriver(
            device="/dev/ttyS0",
            baudrate=115200,
            led_pin=None
        )

        print("云台系统初始化完成")

    def calibrate_imu(self):
        """校准IMU陀螺仪"""
        print("开始IMU校准...")
        if not self.sensor.calib_gyro_exists():
            print("请保持设备静止，正在校准陀螺仪，等待10秒...")
            self.sensor.calib_gyro(10000)
            print("IMU校准完成")
        else:
            self.sensor.load_calib_gyro()
            print("加载已有的IMU校准数据")

    def initialize_motors(self):
        """初始化电机：设置零点并启用"""
        print("正在初始化电机...")

        # 设置电机控制模式为位置控制模式
        print("设置电机控制模式...")
        self.motor.modify_control_mode(1, True, 3)  # 电机1，位置控制模式
        time.sleep(0.1)
        self.motor.modify_control_mode(2, True, 3)  # 电机2，位置控制模式
        time.sleep(0.1)

        # 将当前位置设为零点
        print("设置电机零点...")
        self.motor.reset_current_position(1)  # 电机1零点
        time.sleep(0.1)
        self.motor.reset_current_position(2)  # 电机2零点
        time.sleep(0.1)

        # 启用电机
        print("启用电机...")
        self.motor.enable_motor(1, True, False)  # 启用电机1
        time.sleep(0.1)
        self.motor.enable_motor(2, True, False)  # 启用电机2
        time.sleep(0.1)

        self.is_initialized = True
        print("电机初始化完成")

    def pid_control(self, pid_params, error, dt):
        """PID控制算法"""
        # 比例项
        p_term = pid_params['kp'] * error

        # 积分项
        pid_params['integral'] += error * dt
        # 积分限幅
        if pid_params['integral'] > pid_params['max_integral']:
            pid_params['integral'] = pid_params['max_integral']
        elif pid_params['integral'] < -pid_params['max_integral']:
            pid_params['integral'] = -pid_params['max_integral']
        i_term = pid_params['ki'] * pid_params['integral']

        # 微分项
        d_term = pid_params['kd'] * (error - pid_params['last_error']) / dt if dt > 0 else 0
        pid_params['last_error'] = error

        # PID输出
        output = p_term + i_term + d_term
        return output

    def control_motor_position(self, motor_id, angle_degrees):
        """控制电机到指定角度位置"""
        # 将角度转换为脉冲数
        pulses = int(abs(angle_degrees) * self.angle_to_pulse)

        # 确定方向：正角度为CW(0)，负角度为CCW(1)
        direction = 0 if angle_degrees >= 0 else 1

        # 发送位置控制命令
        self.motor.position_control(
            addr=motor_id,
            direction=direction,
            velocity=self.motor_speed,
            acceleration=self.motor_acceleration,
            pulses=pulses,
            relative_flag=False,  # 绝对位置控制
            sync_flag=False
        )

    def update_stabilization(self, pitch_angle, roll_angle, dt):
        """更新稳定控制"""
        if not self.stabilization_enabled:
            return

        # 计算误差（目标角度 - 当前角度）
        pitch_error = self.target_pitch - pitch_angle
        roll_error = self.target_roll - roll_angle

        # PID控制计算
        pitch_output = self.pid_control(self.pid_pitch, pitch_error, dt)
        roll_output = self.pid_control(self.pid_roll, roll_error, dt)

        # 限制输出角度范围
        pitch_output = max(-90, min(90, pitch_output))
        roll_output = max(-90, min(90, roll_output))

        # 控制电机
        # 电机1控制roll轴（水平方向）
        # 电机2控制pitch轴（竖直方向）
        self.control_motor_position(1, roll_output)
        self.control_motor_position(2, pitch_output)

        # 更新当前角度
        self.current_pitch = pitch_angle
        self.current_roll = roll_angle

    def enable_stabilization(self):
        """启用自稳定功能"""
        if self.is_initialized:
            self.stabilization_enabled = True
            print("自稳定功能已启用")
        else:
            print("请先初始化电机")

    def disable_stabilization(self):
        """禁用自稳定功能"""
        self.stabilization_enabled = False
        print("自稳定功能已禁用")

    def set_target_angles(self, pitch=0.0, roll=0.0):
        """设置目标角度"""
        self.target_pitch = pitch
        self.target_roll = roll
        print(f"目标角度设置为: Pitch={pitch:.1f}°, Roll={roll:.1f}°")

    def run(self):
        """主运行循环"""
        print("开始运行自稳云台系统...")

        # 校准IMU
        self.calibrate_imu()

        # 初始化电机
        self.initialize_motors()

        # 等待系统稳定
        print("系统稳定中，等待3秒...")
        time.sleep(3)

        # 启用自稳定
        self.enable_stabilization()

        last_time = time.ticks_ms() / 1000.0

        print("开始自稳定控制...")
        print("按Ctrl+C退出程序")

        try:
            while True:
                # 读取IMU数据
                data = self.sensor.read_all(calib_gryo=True, radian=True)

                # 计算当前时间和时间差
                current_time = time.ticks_ms() / 1000.0
                dt = current_time - last_time
                last_time = current_time

                # 获取姿态角度
                angle = self.ahrs_filter.get_angle(data.acc, data.gyro, data.mag, dt, radian=False)

                # 更新稳定控制
                self.update_stabilization(angle.x, angle.y, dt)

                # 打印状态信息
                if int(current_time * 10) % 10 == 0:  # 每100ms打印一次
                    status = "启用" if self.stabilization_enabled else "禁用"
                    print(f"Pitch: {angle.x:6.2f}°, Roll: {angle.y:6.2f}°, Yaw: {angle.z:6.2f}°, "
                          f"稳定: {status}, dt: {int(dt*1000):3d}ms")

                # 短暂延时
                time.sleep(0.01)  # 10ms

        except KeyboardInterrupt:
            print("\n正在停止云台系统...")
            self.disable_stabilization()
            # 停止电机
            self.motor.stop_motor(1, False)
            self.motor.stop_motor(2, False)
            print("云台系统已停止")


# 主程序入口
if __name__ == "__main__":
    gimbal = StabilizedGimbal()
    gimbal.run()
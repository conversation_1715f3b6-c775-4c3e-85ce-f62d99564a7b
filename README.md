# 自稳步进云台控制系统

这是一个基于MaixCAM Pro的自稳步进云台控制系统，使用IMU传感器实现云台的自动稳定功能。

## 系统组成

- **硬件**: MaixCAM Pro + IMU传感器(qmi8658) + 两个步进电机
- **软件**: MaixPy + 自定义电机驱动库

## 文件说明

### 主要程序文件

1. **main.py** - 完整版自稳云台控制程序
   - 包含完整的PID控制算法
   - 支持参数调节和高级功能
   - 适合需要精确控制的应用

2. **simple_gimbal.py** - 简化版自稳云台程序 ⭐ **推荐新手使用**
   - 简化的控制算法
   - 更容易理解和调试
   - 适合快速验证功能

3. **motor_driver.py** - 电机驱动库
   - 封装了步进电机的所有控制功能
   - 支持位置控制、速度控制等模式

### 测试和调试程序

4. **gimbal_test.py** - 电机功能测试程序
   - 测试电机初始化和基本控制
   - 验证电机零点设置
   - 测试电机运动范围

5. **pid_tuning.py** - PID参数调试程序
   - 用于调试和优化PID控制参数
   - 提供阶跃响应测试
   - 帮助找到最佳控制参数

## 快速开始

### 1. 硬件连接

```
MaixCAM Pro UART0 ↔ 步进电机驱动板
- 电机1: 控制水平方向旋转 (Roll轴)
- 电机2: 控制竖直方向旋转 (Pitch轴)
- IMU传感器: 安装在云台末端
```

### 2. 运行测试

首先运行电机测试程序，确保硬件连接正常：

```bash
python gimbal_test.py
```

### 3. 运行简化版云台程序

```bash
python simple_gimbal.py
```

### 4. 运行完整版云台程序

```bash
python main.py
```

## 程序功能特点

### 自动初始化流程

1. **IMU校准**: 自动校准陀螺仪偏差
2. **电机设置**: 设置位置控制模式
3. **零点设置**: 将当前位置设为零点
4. **电机启用**: 启用两个电机

### 自稳定控制

- **实时角度检测**: 通过IMU获取云台姿态
- **误差计算**: 计算当前角度与目标角度的偏差
- **控制输出**: 通过PID算法计算电机修正角度
- **平滑控制**: 使用适当的速度和加速度控制电机

### 安全保护

- **角度限制**: 限制电机运动范围，防止过度旋转
- **平滑控制**: 使用较低的速度和加速度，避免突然运动
- **异常处理**: 捕获异常并安全停止电机

## 参数调节

### simple_gimbal.py 参数

```python
self.stabilization_gain = 0.8    # 稳定增益 (0.1-2.0)
self.max_correction_angle = 30   # 最大修正角度 (10-45度)
self.filter_alpha = 0.7          # 角度滤波系数 (0.1-0.9)
```

### main.py PID参数

```python
self.pid_pitch = {
    'kp': 1.5,     # 比例系数 (0.5-3.0)
    'ki': 0.1,     # 积分系数 (0.01-0.5)
    'kd': 0.05,    # 微分系数 (0.01-0.2)
}
```

## 调试建议

### 1. 逐步测试

1. 先运行 `gimbal_test.py` 确保电机正常
2. 再运行 `simple_gimbal.py` 测试基本自稳功能
3. 最后使用 `main.py` 进行精确控制

### 2. 参数调节

- **增益过小**: 响应慢，稳定效果差
- **增益过大**: 震荡，不稳定
- **建议**: 从小参数开始，逐步增加

### 3. 常见问题

**问题**: 电机不动
- 检查串口连接
- 确认电机地址设置
- 检查电源供应

**问题**: 云台震荡
- 降低控制增益
- 增加滤波系数
- 检查机械结构

**问题**: 响应慢
- 增加控制增益
- 减少更新间隔
- 优化PID参数

## 系统架构

```
IMU传感器 → AHRS滤波 → 角度计算 → PID控制 → 电机驱动 → 云台运动
    ↑                                                      ↓
    └─────────────── 反馈控制环路 ←─────────────────────────┘
```

## 注意事项

1. **上电顺序**: 先连接硬件，再运行程序
2. **校准环境**: IMU校准时保持设备完全静止
3. **机械限位**: 确保云台机械结构不会卡死
4. **电源稳定**: 确保电机驱动板电源稳定
5. **安全操作**: 测试时注意云台运动范围，避免碰撞

## 扩展功能

- 添加遥控功能
- 实现目标跟踪
- 增加多轴控制
- 集成相机稳定
- 添加手动控制模式

## 技术支持

如果您在使用过程中遇到问题，请检查：

1. 硬件连接是否正确
2. 电源供应是否稳定
3. 程序参数是否合适
4. IMU校准是否成功

建议从简单的测试程序开始，逐步验证各个功能模块。

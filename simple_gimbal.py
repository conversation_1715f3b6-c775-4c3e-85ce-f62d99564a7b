'''
简化版自稳云台控制程序
专注于IMU数据处理和电机控制的基本集成
'''

from maix import ahrs, time
from maix.ext_dev import imu
from motor_driver import MotorDriver

class SimpleGimbal:
    def __init__(self):
        print("初始化简化版自稳云台...")
        
        # 控制参数
        self.stabilization_gain = 0.8  # 稳定增益，控制响应强度
        self.max_correction_angle = 30  # 最大修正角度
        self.update_interval = 0.05    # 更新间隔50ms
        
        # 目标角度（水平状态）
        self.target_pitch = 0.0
        self.target_roll = 0.0
        
        # 角度滤波
        self.pitch_filter = 0.0
        self.roll_filter = 0.0
        self.filter_alpha = 0.7  # 滤波系数
        
        # 初始化IMU
        self.init_imu()
        
        # 初始化电机
        self.init_motors()
        
        print("简化版云台初始化完成")
    
    def init_imu(self):
        """初始化IMU传感器"""
        print("初始化IMU传感器...")
        
        self.sensor = imu.IMU("qmi8658", mode=imu.Mode.DUAL,
                             acc_scale=imu.AccScale.ACC_SCALE_2G,
                             acc_odr=imu.AccOdr.ACC_ODR_1000,
                             gyro_scale=imu.GyroScale.GYRO_SCALE_256DPS,
                             gyro_odr=imu.GyroOdr.GYRO_ODR_8000)
        
        self.ahrs_filter = ahrs.MahonyAHRS(2.0, 0.01)
        
        # 校准陀螺仪
        if not self.sensor.calib_gyro_exists():
            print("校准陀螺仪，请保持设备静止10秒...")
            self.sensor.calib_gyro(10000)
            print("陀螺仪校准完成")
        else:
            self.sensor.load_calib_gyro()
            print("加载陀螺仪校准数据")
    
    def init_motors(self):
        """初始化电机"""
        print("初始化电机...")
        
        self.motor = MotorDriver(device="/dev/ttyS0", baudrate=115200)
        
        # 设置位置控制模式
        self.motor.modify_control_mode(1, True, 3)  # 电机1 - Roll轴
        self.motor.modify_control_mode(2, True, 3)  # 电机2 - Pitch轴
        time.sleep(0.3)
        
        # 设置零点
        self.motor.reset_current_position(1)
        self.motor.reset_current_position(2)
        time.sleep(0.3)
        
        # 启用电机
        self.motor.enable_motor(1, True, False)
        self.motor.enable_motor(2, True, False)
        time.sleep(0.3)
        
        print("电机初始化完成")
    
    def get_filtered_angles(self, dt):
        """获取滤波后的角度"""
        # 读取IMU数据
        data = self.sensor.read_all(calib_gryo=True, radian=True)
        
        # 计算姿态角度
        angle = self.ahrs_filter.get_angle(data.acc, data.gyro, data.mag, dt, radian=False)
        
        # 低通滤波
        self.pitch_filter = self.filter_alpha * self.pitch_filter + (1 - self.filter_alpha) * angle.x
        self.roll_filter = self.filter_alpha * self.roll_filter + (1 - self.filter_alpha) * angle.y
        
        return self.pitch_filter, self.roll_filter, angle.z
    
    def calculate_correction(self, current_angle, target_angle):
        """计算修正角度"""
        error = target_angle - current_angle
        correction = error * self.stabilization_gain
        
        # 限制修正角度范围
        correction = max(-self.max_correction_angle, min(self.max_correction_angle, correction))
        
        return correction
    
    def control_motor_smooth(self, motor_id, target_angle):
        """平滑控制电机到目标角度"""
        # 限制角度范围
        target_angle = max(-45, min(45, target_angle))
        
        # 转换为脉冲数
        pulses = int(abs(target_angle) * 65536 / 360)
        direction = 0 if target_angle >= 0 else 1
        
        # 使用较低的速度和加速度实现平滑控制
        self.motor.position_control(
            addr=motor_id,
            direction=direction,
            velocity=120,  # 降低速度
            acceleration=30,  # 降低加速度
            pulses=pulses,
            relative_flag=False,
            sync_flag=False
        )
    
    def run_stabilization(self):
        """运行自稳定控制"""
        print("开始自稳定控制...")
        print("系统稳定中，等待3秒...")
        time.sleep(3)
        
        last_time = time.ticks_ms() / 1000.0
        loop_count = 0
        
        print("自稳定已启动，按Ctrl+C停止")
        print("格式: 时间 | Pitch | Roll | Yaw | 修正Pitch | 修正Roll")
        
        try:
            while True:
                current_time = time.ticks_ms() / 1000.0
                dt = current_time - last_time
                last_time = current_time
                
                # 获取滤波后的角度
                pitch, roll, yaw = self.get_filtered_angles(dt)
                
                # 计算修正角度
                pitch_correction = self.calculate_correction(pitch, self.target_pitch)
                roll_correction = self.calculate_correction(roll, self.target_roll)
                
                # 控制电机
                self.control_motor_smooth(2, pitch_correction)  # 电机2控制Pitch
                self.control_motor_smooth(1, roll_correction)   # 电机1控制Roll
                
                # 每秒打印10次状态
                if loop_count % 5 == 0:
                    print(f"{current_time:6.1f} | {pitch:6.2f} | {roll:6.2f} | {yaw:6.2f} | "
                          f"{pitch_correction:6.2f} | {roll_correction:6.2f}")
                
                loop_count += 1
                time.sleep(self.update_interval)
                
        except KeyboardInterrupt:
            print("\n正在停止自稳定...")
            self.stop()
    
    def stop(self):
        """停止云台"""
        print("停止电机...")
        self.motor.stop_motor(1, False)
        self.motor.stop_motor(2, False)
        print("云台已停止")
    
    def test_basic_movement(self):
        """测试基本运动功能"""
        print("\n=== 基本运动测试 ===")
        
        test_angles = [0, 10, 20, 10, 0, -10, -20, -10, 0]
        
        for angle in test_angles:
            print(f"移动到 {angle}°...")
            self.control_motor_smooth(1, angle)  # 测试电机1
            self.control_motor_smooth(2, angle)  # 测试电机2
            time.sleep(2)
            
            # 读取当前位置
            pos1, pos2 = self.motor.get_real_time_position()
            print(f"电机位置 - 1: {pos1:.1f}°, 2: {pos2:.1f}°")
        
        print("基本运动测试完成")
    
    def calibrate_level(self):
        """校准水平位置"""
        print("\n=== 校准水平位置 ===")
        print("请将云台调整到水平位置，然后按回车...")
        # 在MaixPy环境中，我们直接进行校准
        print("开始校准...")
        
        # 读取当前角度作为目标角度
        last_time = time.ticks_ms() / 1000.0
        pitch_sum = 0
        roll_sum = 0
        samples = 50
        
        for i in range(samples):
            current_time = time.ticks_ms() / 1000.0
            dt = current_time - last_time
            last_time = current_time
            
            pitch, roll, _ = self.get_filtered_angles(dt)
            pitch_sum += pitch
            roll_sum += roll
            
            time.sleep(0.02)
        
        self.target_pitch = pitch_sum / samples
        self.target_roll = roll_sum / samples
        
        print(f"水平位置已校准: Pitch={self.target_pitch:.2f}°, Roll={self.target_roll:.2f}°")

def main():
    """主程序"""
    gimbal = SimpleGimbal()
    
    try:
        # 测试基本运动
        gimbal.test_basic_movement()
        
        # 校准水平位置
        gimbal.calibrate_level()
        
        # 运行自稳定
        gimbal.run_stabilization()
        
    except Exception as e:
        print(f"程序运行错误: {e}")
        gimbal.stop()

if __name__ == "__main__":
    main()

'''
云台系统测试程序
用于测试电机初始化、零点设置和基本控制功能
'''

from motor_driver import MotorDriver
from maix import time

def test_motor_initialization():
    """测试电机初始化功能"""
    print("=== 云台电机初始化测试 ===")
    
    # 创建电机驱动实例
    motor = MotorDriver(
        device="/dev/ttyS0",
        baudrate=115200,
        led_pin=None
    )
    
    print("1. 设置电机控制模式为位置控制...")
    # 设置电机1为位置控制模式
    motor.modify_control_mode(1, True, 3)
    time.sleep(0.2)
    
    # 设置电机2为位置控制模式  
    motor.modify_control_mode(2, True, 3)
    time.sleep(0.2)
    
    print("2. 将当前位置设为零点...")
    # 电机1零点设置
    motor.reset_current_position(1)
    time.sleep(0.2)
    
    # 电机2零点设置
    motor.reset_current_position(2)
    time.sleep(0.2)
    
    print("3. 启用电机...")
    # 启用电机1
    motor.enable_motor(1, True, False)
    time.sleep(0.2)
    
    # 启用电机2
    motor.enable_motor(2, True, False)
    time.sleep(0.2)
    
    print("4. 读取电机当前位置...")
    for i in range(5):
        pos1, pos2 = motor.get_real_time_position()
        print(f"电机1位置: {pos1:.2f}°, 电机2位置: {pos2:.2f}°")
        time.sleep(0.5)
    
    print("5. 测试电机小幅度运动...")
    # 电机1转动10度
    pulses = int(10 * 65536 / 360)  # 10度对应的脉冲数
    motor.position_control(1, 0, 100, 30, pulses, False, False)
    time.sleep(2)
    
    # 电机2转动10度
    motor.position_control(2, 0, 100, 30, pulses, False, False)
    time.sleep(2)
    
    print("6. 读取运动后位置...")
    for i in range(3):
        pos1, pos2 = motor.get_real_time_position()
        print(f"电机1位置: {pos1:.2f}°, 电机2位置: {pos2:.2f}°")
        time.sleep(0.5)
    
    print("7. 返回零位...")
    # 返回零位
    motor.position_control(1, 1, 100, 30, pulses, False, False)
    time.sleep(2)
    motor.position_control(2, 1, 100, 30, pulses, False, False)
    time.sleep(2)
    
    print("8. 最终位置检查...")
    for i in range(3):
        pos1, pos2 = motor.get_real_time_position()
        print(f"电机1位置: {pos1:.2f}°, 电机2位置: {pos2:.2f}°")
        time.sleep(0.5)
    
    print("9. 停止电机...")
    motor.stop_motor(1, False)
    motor.stop_motor(2, False)
    
    print("=== 电机初始化测试完成 ===")

def test_motor_control_range():
    """测试电机控制范围"""
    print("\n=== 电机控制范围测试 ===")
    
    motor = MotorDriver(device="/dev/ttyS0", baudrate=115200)
    
    # 初始化
    motor.modify_control_mode(1, True, 3)
    motor.modify_control_mode(2, True, 3)
    time.sleep(0.2)
    
    motor.reset_current_position(1)
    motor.reset_current_position(2)
    time.sleep(0.2)
    
    motor.enable_motor(1, True, False)
    motor.enable_motor(2, True, False)
    time.sleep(0.2)
    
    # 测试不同角度
    test_angles = [0, 15, 30, 45, 0, -15, -30, -45, 0]
    
    for angle in test_angles:
        print(f"移动到 {angle}°...")
        pulses = int(abs(angle) * 65536 / 360)
        direction = 0 if angle >= 0 else 1
        
        # 同时控制两个电机
        motor.position_control(1, direction, 150, 40, pulses, False, False)
        time.sleep(3)
        motor.position_control(2, direction, 150, 40, pulses, False, False)
        
        time.sleep(3)  # 等待运动完成
        
        # 读取位置
        pos1, pos2 = motor.get_real_time_position()
        print(f"目标: {angle}°, 实际位置 - 电机1: {pos1:.2f}°, 电机2: {pos2:.2f}°")
        time.sleep(1)
    
    # 停止电机
    motor.stop_motor(1, False)
    motor.stop_motor(2, False)
    
    print("=== 电机控制范围测试完成 ===")

if __name__ == "__main__":
    try:
        # 运行基本初始化测试
        test_motor_initialization()
        
        # 询问是否继续范围测试
        print("\n是否继续进行电机控制范围测试？(y/n)")
        # 由于MaixPy环境限制，这里直接运行范围测试
        print("自动运行范围测试...")
        time.sleep(2)
        test_motor_control_range()
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
    
    print("测试程序结束")

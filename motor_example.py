"""
MaixCAM Pro 步进电机控制示例
使用 motor_driver.py 驱动库
通过单个串口控制双电机，使用地址区分
"""

import time
from motor_driver import MotorDriver


def main():
    """主函数"""
    print("初始化步进电机驱动...")

    # 创建电机驱动实例
    # 使用UART0，通过地址控制不同电机
    motor = MotorDriver(
        device="/dev/ttyS0",  # 使用UART0
        baudrate=115200,      # 波特率
        led_pin="None"         # LED指示灯引脚（可选，设为None禁用）
    )
    
    print("电机驱动初始化完成")
    
    # 点亮LED指示灯
    # motor.set_led(True)
    
    try:
        # 示例1: 使能电机
        print("使能电机...")
        motor.enable_motor(1, True, False)  # 使能电机1
        motor.enable_motor(2, True, False)  # 使能电机2
        time.sleep(1)
        
        # 示例2: 位置清零
        print("位置清零...")
        motor.reset_current_position(1)
        motor.reset_current_position(2)
        time.sleep(1)
        
        # 示例3: 速度控制
        print("速度控制测试...")
        motor.velocity_control(1, 0, 500, 50, False)  # 电机1，CW方向，500RPM，加速度50
        motor.velocity_control(2, 1, 500, 50, False)  # 电机2，CCW方向，500RPM，加速度50
        time.sleep(3)
        
        # 停止电机
        print("停止电机...")
        motor.stop_motor(1, False)
        motor.stop_motor(2, False)
        time.sleep(1)
        
        # 示例4: 位置控制
        print("位置控制测试...")
        motor.position_control(1, 0, 300, 30, 2000, True, False)  # 电机1相对运动2000脉冲
        motor.position_control(2, 1, 300, 30, 2000, True, False)  # 电机2相对运动2000脉冲
        time.sleep(5)
        
        # 示例5: 实时位置监控
        print("实时位置监控（10秒）...")
        start_time = time.time()
        while time.time() - start_time < 10:
            motor.print_position()
            time.sleep(0.5)
        
        # 示例6: 回零操作
        print("回零操作...")
        motor.trigger_origin_return(1, 0, False)  # 电机1回零
        motor.trigger_origin_return(2, 0, False)  # 电机2回零
        time.sleep(5)
        
        # 示例7: 多机同步运动
        print("多机同步运动测试...")
        motor.position_control(1, 0, 200, 20, 1000, True, True)  # 电机1，启用同步标志
        motor.position_control(2, 0, 200, 20, 1000, True, True)  # 电机2，启用同步标志
        motor.synchronous_motion(1)  # 执行同步运动
        time.sleep(3)
        
    except KeyboardInterrupt:
        print("用户中断程序")
    except Exception as e:
        print(f"发生错误: {e}")
    finally:
        # 停止所有电机
        print("停止所有电机...")
        motor.stop_motor(1, False)
        motor.stop_motor(2, False)
        
        # 禁用电机
        motor.enable_motor(1, False, False)
        motor.enable_motor(2, False, False)
        
        # 关闭LED
        motor.set_led(False)
        print("程序结束")


def simple_control_example():
    """简单控制示例"""
    print("简单控制示例...")

    # 创建电机驱动实例
    motor = MotorDriver(device="/dev/ttyS0", baudrate=115200)

    # 使能电机
    motor.enable_motor(1, True, False)

    # 速度控制 - 电机1以300RPM速度顺时针旋转
    motor.velocity_control(1, 0, 300, 30, False)
    time.sleep(2)

    # 停止电机
    motor.stop_motor(1, False)

    # 位置控制 - 电机1相对运动1000脉冲
    motor.position_control(1, 0, 200, 20, 1000, True, False)
    time.sleep(3)

    # 获取当前位置
    pos1, pos2 = motor.get_real_time_position()
    print(f"当前位置 - 电机1: {pos1:.1f}°, 电机2: {pos2:.1f}°")

    # 禁用电机
    motor.enable_motor(1, False, False)


def position_monitoring():
    """位置监控示例"""
    print("位置监控示例...")

    motor = MotorDriver(device="/dev/ttyS0", baudrate=115200)

    # 使能电机
    motor.enable_motor(1, True, False)
    motor.enable_motor(2, True, False)

    # 启动连续运动
    motor.velocity_control(1, 0, 100, 10, False)  # 慢速旋转
    motor.velocity_control(2, 1, 100, 10, False)  # 反向慢速旋转

    # 监控位置10秒
    print("开始监控位置...")
    for i in range(20):
        motor.print_position()
        time.sleep(0.5)

    # 停止电机
    motor.stop_motor(1, False)
    motor.stop_motor(2, False)
    motor.enable_motor(1, False, False)
    motor.enable_motor(2, False, False)


if __name__ == "__main__":
    # 运行主示例
    main()
    
    # 或者运行简单示例
    # simple_control_example()
    
    # 或者运行位置监控示例
    # position_monitoring()

'''
PID参数调试程序
用于测试和调整云台PID控制参数
'''

from maix import ahrs, time
from maix.ext_dev import imu
from motor_driver import MotorDriver

class PIDTuner:
    def __init__(self):
        # 可调节的PID参数
        self.pid_params = {
            'kp': 1.0,     # 比例系数
            'ki': 0.05,    # 积分系数  
            'kd': 0.02,    # 微分系数
            'integral': 0.0,
            'last_error': 0.0,
            'max_integral': 50.0
        }
        
        # 目标角度
        self.target_angle = 0.0
        
        # 初始化IMU
        print("初始化IMU...")
        self.sensor = imu.IMU("qmi8658", mode=imu.Mode.DUAL,
                             acc_scale=imu.AccScale.ACC_SCALE_2G,
                             acc_odr=imu.AccOdr.ACC_ODR_1000,
                             gyro_scale=imu.GyroScale.GYRO_SCALE_256DPS,
                             gyro_odr=imu.GyroOdr.GYRO_ODR_8000)
        
        self.ahrs_filter = ahrs.MahonyAHRS(2.0, 0.01)
        
        # 初始化电机
        print("初始化电机...")
        self.motor = MotorDriver(device="/dev/ttyS0", baudrate=115200)
        
        # 校准IMU
        if not self.sensor.calib_gyro_exists():
            print("校准IMU，请保持静止10秒...")
            self.sensor.calib_gyro(10000)
        else:
            self.sensor.load_calib_gyro()
        
        # 初始化电机
        self.setup_motors()
    
    def setup_motors(self):
        """设置电机"""
        # 设置位置控制模式
        self.motor.modify_control_mode(1, True, 3)
        self.motor.modify_control_mode(2, True, 3)
        time.sleep(0.2)
        
        # 设置零点
        self.motor.reset_current_position(1)
        self.motor.reset_current_position(2)
        time.sleep(0.2)
        
        # 启用电机
        self.motor.enable_motor(1, True, False)
        self.motor.enable_motor(2, True, False)
        time.sleep(0.2)
        
        print("电机设置完成")
    
    def pid_control(self, error, dt):
        """PID控制算法"""
        # 比例项
        p_term = self.pid_params['kp'] * error
        
        # 积分项
        self.pid_params['integral'] += error * dt
        # 积分限幅
        if self.pid_params['integral'] > self.pid_params['max_integral']:
            self.pid_params['integral'] = self.pid_params['max_integral']
        elif self.pid_params['integral'] < -self.pid_params['max_integral']:
            self.pid_params['integral'] = -self.pid_params['max_integral']
        i_term = self.pid_params['ki'] * self.pid_params['integral']
        
        # 微分项
        d_term = self.pid_params['kd'] * (error - self.pid_params['last_error']) / dt if dt > 0 else 0
        self.pid_params['last_error'] = error
        
        # PID输出
        output = p_term + i_term + d_term
        
        return output, p_term, i_term, d_term
    
    def control_motor(self, motor_id, angle_degrees):
        """控制电机到指定角度"""
        # 限制角度范围
        angle_degrees = max(-90, min(90, angle_degrees))
        
        # 转换为脉冲数
        pulses = int(abs(angle_degrees) * 65536 / 360)
        direction = 0 if angle_degrees >= 0 else 1
        
        # 发送控制命令
        self.motor.position_control(
            addr=motor_id,
            direction=direction,
            velocity=200,
            acceleration=50,
            pulses=pulses,
            relative_flag=False,
            sync_flag=False
        )
    
    def test_step_response(self, motor_id=2, step_angle=10.0):
        """测试阶跃响应"""
        print(f"\n=== 测试电机{motor_id}阶跃响应 ===")
        print(f"目标角度: {step_angle}°")
        print(f"PID参数: Kp={self.pid_params['kp']}, Ki={self.pid_params['ki']}, Kd={self.pid_params['kd']}")
        
        self.target_angle = step_angle
        self.pid_params['integral'] = 0.0
        self.pid_params['last_error'] = 0.0
        
        last_time = time.ticks_ms() / 1000.0
        start_time = last_time
        
        print("时间(s), 目标(°), 当前(°), 误差(°), PID输出, P项, I项, D项")
        
        for i in range(200):  # 运行20秒
            # 读取IMU数据
            data = self.sensor.read_all(calib_gryo=True, radian=True)
            
            current_time = time.ticks_ms() / 1000.0
            dt = current_time - last_time
            last_time = current_time
            
            # 获取角度
            angle = self.ahrs_filter.get_angle(data.acc, data.gyro, data.mag, dt, radian=False)
            current_angle = angle.x if motor_id == 2 else angle.y  # pitch或roll
            
            # 计算误差
            error = self.target_angle - current_angle
            
            # PID控制
            pid_output, p_term, i_term, d_term = self.pid_control(error, dt)
            
            # 控制电机
            self.control_motor(motor_id, pid_output)
            
            # 每0.5秒打印一次数据
            if i % 5 == 0:
                elapsed = current_time - start_time
                print(f"{elapsed:6.2f}, {self.target_angle:6.2f}, {current_angle:6.2f}, "
                      f"{error:6.2f}, {pid_output:6.2f}, {p_term:6.2f}, {i_term:6.2f}, {d_term:6.2f}")
            
            time.sleep(0.1)
        
        # 返回零位
        print("返回零位...")
        self.target_angle = 0.0
        for i in range(50):
            data = self.sensor.read_all(calib_gryo=True, radian=True)
            current_time = time.ticks_ms() / 1000.0
            dt = current_time - last_time
            last_time = current_time
            
            angle = self.ahrs_filter.get_angle(data.acc, data.gyro, data.mag, dt, radian=False)
            current_angle = angle.x if motor_id == 2 else angle.y
            
            error = self.target_angle - current_angle
            pid_output, _, _, _ = self.pid_control(error, dt)
            self.control_motor(motor_id, pid_output)
            
            time.sleep(0.1)
    
    def tune_parameters(self):
        """参数调试主程序"""
        print("=== PID参数调试程序 ===")
        print("将测试不同的PID参数组合")
        
        # 测试不同的参数组合
        test_params = [
            {'kp': 0.5, 'ki': 0.02, 'kd': 0.01},
            {'kp': 1.0, 'ki': 0.05, 'kd': 0.02},
            {'kp': 1.5, 'ki': 0.08, 'kd': 0.03},
            {'kp': 2.0, 'ki': 0.1, 'kd': 0.05},
        ]
        
        for i, params in enumerate(test_params):
            print(f"\n--- 测试参数组合 {i+1} ---")
            self.pid_params.update(params)
            
            # 测试pitch轴（电机2）
            self.test_step_response(motor_id=2, step_angle=15.0)
            
            print("等待5秒后进行下一组测试...")
            time.sleep(5)
        
        print("\n=== 参数调试完成 ===")

if __name__ == "__main__":
    try:
        tuner = PIDTuner()
        tuner.tune_parameters()
        
    except KeyboardInterrupt:
        print("\n调试被用户中断")
    except Exception as e:
        print(f"调试过程中发生错误: {e}")
    
    print("PID调试程序结束")
